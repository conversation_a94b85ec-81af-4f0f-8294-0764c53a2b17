// pages/profile/profile.js
const app = getApp()
const auth = require('../../utils/auth.js')
const storage = require('../../utils/storage.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,
    userStats: {
      totalOrders: 0,
      totalFlightTime: 0,
      worksCount: 0
    },
    
    // 其他数据
    couponCount: 0,
    
    // 页面状态
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('个人中心页面加载')
    this.checkAuth()
    this.loadUserData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.checkAuth()
    this.refreshUserData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 检查认证状态
   */
  checkAuth() {
    if (!auth.checkLoginStatus()) {
      auth.redirectToLogin('/pages/profile/profile')
      return false
    }
    return true
  },

  /**
   * 加载用户数据
   */
  async loadUserData() {
    if (!this.checkAuth()) return

    try {
      // 获取用户基本信息
      const userInfo = auth.getCurrentUser()
      if (userInfo) {
        this.setData({ userInfo })
      }

      // 加载用户统计数据
      await this.loadUserStats()
      
      console.log('个人中心数据加载完成')
    } catch (error) {
      console.error('加载用户数据失败：', error)
    }
  },

  /**
   * 刷新用户数据
   */
  async refreshUserData() {
    try {
      // 静默刷新用户信息
      const userInfo = auth.getCurrentUser()
      if (userInfo) {
        this.setData({ userInfo })
      }

      // 刷新用户统计数据（包括作品数量）
      await this.loadUserStats()
    } catch (error) {
      console.error('刷新用户数据失败：', error)
    }
  },

  /**
   * 加载用户统计
   */
  async loadUserStats() {
    try {
      const userInfo = this.data.userInfo
      if (userInfo) {
        // 从作品集获取真实的作品数量
        const galleryManager = require('../../utils/galleryManager')
        const userGallery = galleryManager.getUserGallery()
        const actualWorksCount = userGallery.length

        const userStats = {
          totalOrders: userInfo.totalOrders || 25,
          totalFlightTime: userInfo.totalFlightTime || 120,
          worksCount: actualWorksCount // 使用真实的作品数量
        }

        this.setData({
          userStats,
          couponCount: 3 // 模拟优惠券数量
        })

        console.log('用户统计数据更新：', userStats)
      }
    } catch (error) {
      console.error('加载用户统计失败：', error)
    }
  },

  /**
   * 编辑个人资料
   */
  editProfile() {
    wx.showModal({
      title: '编辑资料',
      content: '个人资料编辑功能正在开发中',
      showCancel: false
    })
  },

  /**
   * 充值功能
   */
  recharge() {
    wx.navigateTo({
      url: '/pages/recharge/recharge'
    })
  },

  /**
   * 查看交易明细
   */
  viewTransactions() {
    wx.showModal({
      title: '交易明细',
      content: '• 充值记录\n• 消费记录\n• 退款记录',
      showCancel: false
    })
  },

  /**
   * 页面跳转 - 我的服务
   */
  goToGallery() {
    wx.navigateTo({
      url: '/pages/gallery/gallery'
    })
  },

  goToFavorites() {
    wx.showModal({
      title: '我的收藏',
      content: '• 书圣故里\n• 鲁迅故里\n• 兰亭景区\n• 沈园\n• 东湖景区\n等8个地点',
      showCancel: false
    })
  },

  goToCoupons() {
    wx.showModal({
      title: '我的优惠券',
      content: '• 首次租赁9折券\n• 满200减30券\n• 周末特惠券',
      showCancel: false
    })
  },

  goToMembership() {
    wx.showModal({
      title: '会员特权',
      content: '• 白银会员：9.5折优惠\n• 专属客服\n• 优先预订\n\n升级到黄金会员享受更多特权！',
      showCancel: false
    })
  },

  /**
   * 页面跳转 - 设置
   */
  goToAccountSettings() {
    wx.showModal({
      title: '账户设置',
      content: '• 修改密码\n• 实名认证\n• 绑定邮箱\n• 注销账户',
      showCancel: false
    })
  },

  goToNotifications() {
    wx.showModal({
      title: '消息通知设置',
      content: '• 订单状态通知: 开启\n• 优惠活动通知: 开启\n• 系统消息通知: 开启',
      showCancel: false
    })
  },

  goToPrivacy() {
    wx.showModal({
      title: '隐私安全',
      content: '• 隐私设置\n• 数据管理\n• 安全中心\n• 用户协议',
      showCancel: false
    })
  },

  goToHelp() {
    wx.showModal({
      title: '帮助中心',
      content: '• 常见问题\n• 使用教程\n• 联系客服\n• 意见反馈',
      showCancel: false
    })
  },

  goToAbout() {
    wx.showModal({
      title: '关于逍遥境',
      content: '版本：v1.0.0\n\n专业无人机租赁平台\n让每个人都能体验航拍乐趣\n\n© 2024 逍遥境科技有限公司',
      showCancel: false
    })
  },


})
/* pages/home/<USER>/

.home-container {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  padding-bottom: 32rpx;
}

/* 顶部导航 */
.navbar {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 32rpx;
  padding-top: 64rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border-bottom: 1rpx solid rgba(148, 163, 184, 0.1);
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.brand-section {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.brand-title {
  background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4f46e5 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 48rpx;
  font-weight: 700;
}

.brand-subtitle {
  color: #64748b;
  font-size: 28rpx;
  font-weight: 500;
}

.nav-icons {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.nav-icon {
  font-size: 40rpx;
  color: #64748b;
  padding: 16rpx;
  border-radius: 50%;
  background: rgba(148, 163, 184, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

.nav-icon:active {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  transform: scale(0.95);
  color: #475569;
}

/* 搜索栏 不美观暂时先放着
.search-section {
  padding: 32rpx;
}

.search-bar {
  position: relative;
  background: #ffffff;
  border-radius: 32rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid #f3f4f6;
  height: 88rpx;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #9ca3af;
  z-index: 1;
}

.search-input {
  flex: 1;
  font-size: 32rpx;
  color: #6b7280;
  background: transparent;
  border: none;
  outline: none;
  padding-left: 64rpx;
  height: 100%;
  line-height: 88rpx;
} */

/* 通用区块样式 */
.section {
  padding: 0 48rpx 16rpx;
  margin-bottom: 16rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

/* 热门推荐 - 网格布局 */
.equipment-swiper {
  height: 460rpx;
  padding: 0;
  margin-bottom: 8rpx;
}

.swiper-item {
  width: 100%;
  box-sizing: border-box;
  padding: 0 24rpx;
}

.equipment-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 32rpx;
  padding: 32rpx 32rpx 24rpx 32rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.08), 0 3rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(148, 163, 184, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  display: flex;
  flex-direction: column;
  max-height: 420rpx;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.equipment-card:active {
  transform: translateY(-8rpx) scale(1.02);
  box-shadow: 0 25rpx 60rpx rgba(0, 0, 0, 0.15), 0 10rpx 25rpx rgba(0, 0, 0, 0.08);
  border-color: rgba(99, 102, 241, 0.2);
}

.equipment-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.equipment-card:active::before {
  opacity: 1;
}

.equipment-image {
  width: 100%;
  height: 192rpx;
  border-radius: 24rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  flex-shrink: 0;
}

.equipment-photo {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  object-fit: cover;
}

.equipment-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  justify-content: space-between;
}

.equipment-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.equipment-desc {
  font-size: 24rpx;
  color: #6b7280;
  flex: 1;
  line-height: 1.4;
  margin-bottom: 16rpx;
}

.equipment-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.equipment-price {
  font-size: 36rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.price-unit {
  font-size: 28rpx;
  font-weight: 400;
  color: #f59e0b;
}

.rent-btn {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: #ffffff;
  padding: 12rpx 32rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 4rpx 15rpx rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
}

.rent-btn:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(99, 102, 241, 0.4);
}

/* 热门拍摄地点 - 列表布局 */
.location-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.location-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(148, 163, 184, 0.1);
  display: flex;
  align-items: center;
  gap: 32rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.location-card:active {
  transform: translateY(-6rpx) scale(1.01);
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.12), 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  border-color: rgba(6, 182, 212, 0.2);
}

.location-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #06b6d4 0%, #0891b2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.location-card:active::after {
  opacity: 1;
}

.location-image {
  width: 128rpx;
  height: 128rpx;
  border-radius: 24rpx;
  background: #f3f4f6;
  flex-shrink: 0;
}

.location-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.location-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.location-desc {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}

.location-rating {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.rating-stars {
  display: flex;
  gap: 4rpx;
}

.star {
  font-size: 24rpx;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 1rpx 2rpx rgba(251, 191, 36, 0.3);
}

.rating-text {
  font-size: 24rpx;
  color: #6b7280;
}

.location-arrow {
  font-size: 32rpx;
  color: #9ca3af;
}

/* 快速操作 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
}

.action-icon {
  width: 96rpx;
  height: 96rpx;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(148, 163, 184, 0.1);
  transition: all 0.3s ease;
}

.action-item:active .action-icon {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.icon {
  font-size: 48rpx;
  color: #6b7280;
}

.action-text {
  font-size: 24rpx;
  color: #6b7280;
}

/* 搜索弹窗 */
.search-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  padding-top: 200rpx;
}

.search-modal-content {
  background: #ffffff;
  margin: 0 32rpx;
  border-radius: 24rpx;
  padding: 32rpx;
  width: calc(100% - 64rpx);
}

.search-input-wrapper {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.search-input {
  flex: 1;
  padding: 24rpx;
  border: 2rpx solid #f3f4f6;
  border-radius: 16rpx;
  font-size: 32rpx;
}

.search-btn {
  background: #1f2937;
  color: #ffffff;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  border: none;
}

.suggestions-title {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.suggestion-item {
  background: #f3f4f6;
  padding: 16rpx 24rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #6b7280;
}

/* pages/home/<USER>/

.home-container {
  background-color: #f9fafb;
  min-height: 100vh;
  padding-bottom: 32rpx;
}

/* 顶部导航 */
.navbar {
  background: #ffffff;
  padding: 32rpx;
  padding-top: 64rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.brand-section {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.brand-title {
  color: #1f2937;
  font-size: 48rpx;
  font-weight: 700;
}

.brand-subtitle {
  color: #6b7280;
  font-size: 28rpx;
}

.nav-icons {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.nav-icon {
  font-size: 40rpx;
  color: #6b7280;
  padding: 8rpx;
}

/* 搜索栏 */
.search-section {
  padding: 32rpx;
}

.search-bar {
  position: relative;
  background: #ffffff;
  border-radius: 32rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid #f3f4f6;
  height: 88rpx;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #9ca3af;
  z-index: 1;
}

.search-input {
  flex: 1;
  font-size: 32rpx;
  color: #6b7280;
  background: transparent;
  border: none;
  outline: none;
  padding-left: 64rpx;
  height: 100%;
  line-height: 88rpx;
}

/* 通用区块样式 */
.section {
  padding: 0 48rpx 48rpx;
  margin-bottom: 48rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

/* 热门推荐 - 网格布局 */
.equipment-swiper {
  height: 420rpx;
  padding: 0;
  margin-bottom: 24rpx;
}

.swiper-item {
  width: 100%;
  box-sizing: border-box;
  padding: 0 24rpx;
}

.equipment-card {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  max-height: 380rpx;
}

.equipment-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 20rpx 50rpx rgba(0, 0, 0, 0.1);
}

.equipment-image {
  width: 100%;
  height: 192rpx;
  border-radius: 24rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  flex-shrink: 0;
}

.equipment-photo {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  object-fit: cover;
}

.equipment-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
  min-height: 0;
}

.equipment-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.equipment-desc {
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
  flex: 1;
}

.equipment-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.equipment-price {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
}

.price-unit {
  font-size: 28rpx;
  font-weight: 400;
}

.rent-btn {
  background: #1f2937;
  color: #ffffff;
  padding: 8rpx 24rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: none;
}

/* 热门拍摄地点 - 列表布局 */
.location-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.location-card {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 32rpx;
  transition: all 0.3s ease;
}

.location-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 20rpx 50rpx rgba(0, 0, 0, 0.1);
}

.location-image {
  width: 128rpx;
  height: 128rpx;
  border-radius: 24rpx;
  background: #f3f4f6;
  flex-shrink: 0;
}

.location-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.location-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.location-desc {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}

.location-rating {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.rating-stars {
  display: flex;
  gap: 4rpx;
}

.star {
  font-size: 24rpx;
  color: #fbbf24;
}

.rating-text {
  font-size: 24rpx;
  color: #6b7280;
}

.location-arrow {
  font-size: 32rpx;
  color: #9ca3af;
}

/* 快速操作 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  text-align: center;
}

.action-icon {
  width: 96rpx;
  height: 96rpx;
  background: #f3f4f6;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  font-size: 48rpx;
  color: #6b7280;
}

.action-text {
  font-size: 24rpx;
  color: #6b7280;
}

/* 搜索弹窗 */
.search-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  padding-top: 200rpx;
}

.search-modal-content {
  background: #ffffff;
  margin: 0 32rpx;
  border-radius: 24rpx;
  padding: 32rpx;
  width: calc(100% - 64rpx);
}

.search-input-wrapper {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.search-input {
  flex: 1;
  padding: 24rpx;
  border: 2rpx solid #f3f4f6;
  border-radius: 16rpx;
  font-size: 32rpx;
}

.search-btn {
  background: #1f2937;
  color: #ffffff;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  border: none;
}

.suggestions-title {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.suggestion-item {
  background: #f3f4f6;
  padding: 16rpx 24rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #6b7280;
}

/**
 * 逍遥境小程序认证工具模块
 * 提供登录状态管理、权限检查、重定向等功能
 */

const app = getApp()

/**
 * 检查用户登录状态
 * @returns {boolean} 是否已登录
 */
function checkLoginStatus() {
  try {
    const isLoggedIn = wx.getStorageSync('isLoggedIn')
    const userInfo = wx.getStorageSync('userInfo')
    const token = wx.getStorageSync('token')
    const loginTime = wx.getStorageSync('loginTime')
    
    console.log('认证检查：', { isLoggedIn, userInfo: !!userInfo, token: !!token })
    
    // 检查基础登录信息
    if (!isLoggedIn || !userInfo || !token) {
      console.log('基础登录信息不完整')
      return false
    }
    
    // 检查token是否过期（7天有效期）
    const tokenAge = Date.now() - (loginTime || 0)
    const tokenExpired = tokenAge > 7 * 24 * 60 * 60 * 1000
    
    if (tokenExpired) {
      console.log('Token已过期，清除登录状态')
      clearUserInfo()
      return false
    }
    
    // 更新全局状态
    if (app && app.globalData) {
      app.globalData.isLoggedIn = true
      app.globalData.userInfo = userInfo
    }
    
    return true
  } catch (error) {
    console.error('检查登录状态失败：', error)
    return false
  }
}

/**
 * 重定向到登录页
 * @param {string} redirectUrl 登录成功后要跳转的页面
 * @param {object} options 跳转配置
 */
function redirectToLogin(redirectUrl = '', options = {}) {
  console.log('重定向到登录页，目标页面：', redirectUrl)
  
  // 保存目标页面，登录成功后跳转
  if (redirectUrl) {
    try {
      wx.setStorageSync('redirectUrl', redirectUrl)
    } catch (error) {
      console.error('保存重定向URL失败：', error)
    }
  }
  
  // 根据配置选择跳转方式
  const { replace = true, showToast = true } = options
  
  if (showToast) {
    wx.showToast({
      title: '请先登录',
      icon: 'none',
      duration: 1500
    })
  }
  
  // 延迟跳转，确保Toast显示
  setTimeout(() => {
    if (replace) {
      wx.reLaunch({
        url: '/pages/login/login',
        fail: (error) => {
          console.error('跳转到登录页失败：', error)
        }
      })
    } else {
      wx.navigateTo({
        url: '/pages/login/login',
        fail: (error) => {
          console.error('跳转到登录页失败：', error)
          // 如果navigateTo失败，尝试用reLaunch
          wx.reLaunch({
            url: '/pages/login/login'
          })
        }
      })
    }
  }, showToast ? 1500 : 0)
}

/**
 * 登录成功处理
 * @param {object} userInfo 用户信息
 * @param {object} options 登录配置
 */
function handleLoginSuccess(userInfo, options = {}) {
  console.log('处理登录成功：', userInfo.nickname)
  
  try {
    // 验证用户信息完整性
    if (!userInfo || !userInfo.token || !userInfo.id) {
      throw new Error('用户信息不完整')
    }
    
    // 存储到本地
    wx.setStorageSync('isLoggedIn', true)
    wx.setStorageSync('userInfo', userInfo)
    wx.setStorageSync('token', userInfo.token)
    wx.setStorageSync('loginTime', Date.now())
    
    // 更新全局状态
    if (app && app.globalData) {
      app.globalData.isLoggedIn = true
      app.globalData.userInfo = userInfo
    }
    
    // 显示欢迎信息
    const { showWelcome = true } = options
    if (showWelcome) {
      wx.showToast({
        title: `欢迎，${userInfo.nickname || '用户'}`,
        icon: 'success',
        duration: 2000
      })
    }
    
    // 处理登录后重定向
    setTimeout(() => {
      handleLoginRedirect()
    }, showWelcome ? 2000 : 0)
    
    return true
  } catch (error) {
    console.error('登录成功处理失败：', error)
    wx.showToast({
      title: '登录处理失败，请重试',
      icon: 'error'
    })
    return false
  }
}

/**
 * 处理登录后重定向
 */
function handleLoginRedirect() {
  try {
    const redirectUrl = wx.getStorageSync('redirectUrl')
    
    if (redirectUrl) {
      // 清除重定向URL
      wx.removeStorageSync('redirectUrl')
      console.log('登录后重定向到：', redirectUrl)
      
      // 根据页面类型选择跳转方式
      if (isTabBarPage(redirectUrl)) {
        wx.switchTab({
          url: redirectUrl,
          fail: (error) => {
            console.error('重定向失败，跳转到首页：', error)
            wx.switchTab({ url: '/pages/home/<USER>' })
          }
        })
      } else {
        wx.navigateTo({
          url: redirectUrl,
          fail: (error) => {
            console.error('重定向失败，跳转到首页：', error)
            wx.switchTab({ url: '/pages/home/<USER>' })
          }
        })
      }
    } else {
      // 默认跳转到首页
      wx.switchTab({
        url: '/pages/home/<USER>',
        fail: (error) => {
          console.error('跳转到首页失败：', error)
        }
      })
    }
  } catch (error) {
    console.error('重定向处理失败：', error)
    wx.switchTab({ url: '/pages/home/<USER>' })
  }
}

/**
 * 判断是否为TabBar页面
 * @param {string} url 页面路径
 * @returns {boolean}
 */
function isTabBarPage(url) {
  const tabBarPages = [
    '/pages/home/<USER>',
    '/pages/equipment/equipment',
    '/pages/orders/orders',
    '/pages/profile/profile'
  ]
  return tabBarPages.some(page => url.includes(page))
}

/**
 * 退出登录
 * @param {object} options 退出配置
 */
function logout(options = {}) {
  console.log('用户退出登录')
  
  const { showConfirm = true, showToast = true } = options
  
  const performLogout = () => {
    try {
      // 清除本地存储
      clearUserInfo()
      
      // 更新全局状态
      if (app && app.globalData) {
        app.globalData.isLoggedIn = false
        app.globalData.userInfo = null
      }
      
      if (showToast) {
        wx.showToast({
          title: '已退出登录',
          icon: 'success',
          duration: 1500
        })
      }
      
      // 跳转到登录页
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/login/login',
          fail: (error) => {
            console.error('跳转到登录页失败：', error)
          }
        })
      }, showToast ? 1500 : 0)
      
    } catch (error) {
      console.error('退出登录失败：', error)
      wx.showToast({
        title: '退出失败，请重试',
        icon: 'error'
      })
    }
  }
  
  if (showConfirm) {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          performLogout()
        }
      }
    })
  } else {
    performLogout()
  }
}

/**
 * 清除用户信息
 */
function clearUserInfo() {
  try {
    wx.removeStorageSync('isLoggedIn')
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('token')
    wx.removeStorageSync('loginTime')
    wx.removeStorageSync('redirectUrl')
    console.log('用户信息已清除')
  } catch (error) {
    console.error('清除用户信息失败：', error)
  }
}

/**
 * 页面权限检查装饰器
 * @param {boolean} requireLogin 是否需要登录
 * @returns {function} 装饰器函数
 */
function requireLogin(requireLogin = true) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args) {
      if (requireLogin && !checkLoginStatus()) {
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]
        const currentUrl = currentPage ? currentPage.route : ''
        
        redirectToLogin(`/${currentUrl}`)
        return
      }
      
      return originalMethod.apply(this, args)
    }
    
    return descriptor
  }
}

/**
 * 获取当前用户信息
 * @returns {object|null} 用户信息
 */
function getCurrentUser() {
  try {
    if (app && app.globalData && app.globalData.userInfo) {
      return app.globalData.userInfo
    }
    
    const userInfo = wx.getStorageSync('userInfo')
    return userInfo || null
  } catch (error) {
    console.error('获取用户信息失败：', error)
    return null
  }
}

/**
 * 更新用户信息
 * @param {object} userInfo 新的用户信息
 */
function updateUserInfo(userInfo) {
  try {
    const currentUser = getCurrentUser()
    if (!currentUser) {
      console.error('用户未登录，无法更新用户信息')
      return false
    }
    
    const updatedUser = { ...currentUser, ...userInfo }
    
    // 更新本地存储
    wx.setStorageSync('userInfo', updatedUser)
    
    // 更新全局状态
    if (app && app.globalData) {
      app.globalData.userInfo = updatedUser
    }
    
    console.log('用户信息已更新')
    return true
  } catch (error) {
    console.error('更新用户信息失败：', error)
    return false
  }
}

/**
 * 检查特定权限
 * @param {string} permission 权限名称
 * @returns {boolean} 是否有权限
 */
function hasPermission(permission) {
  const user = getCurrentUser()
  if (!user) return false
  
  // 根据用户角色或权限列表检查
  // 这里可以根据实际业务需求实现
  const userPermissions = user.permissions || []
  return userPermissions.includes(permission)
}

module.exports = {
  checkLoginStatus,
  redirectToLogin,
  handleLoginSuccess,
  handleLoginRedirect,
  logout,
  clearUserInfo,
  requireLogin,
  getCurrentUser,
  updateUserInfo,
  hasPermission,
  isTabBarPage
} 
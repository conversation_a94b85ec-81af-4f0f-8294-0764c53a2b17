/* pages/recharge/recharge.wxss */

.recharge-container {
  background-color: #f9fafb;
  min-height: 100vh;
  padding-bottom: 300rpx;
  box-sizing: border-box;
}

/* 顶部导航 */
.nav-header {
  background: #ffffff;
  padding: 32rpx 48rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.nav-content {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.back-btn {
  width: 64rpx;
  height: 64rpx;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 40rpx;
  color: #6b7280;
}

.nav-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #1f2937;
}

/* 当前余额卡片 */
.balance-card {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  margin: 32rpx;
  border-radius: 32rpx;
  padding: 40rpx;
  color: #ffffff;
}

.balance-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.balance-info {
  flex: 1;
}

.balance-title {
  font-size: 36rpx;
  font-weight: 600;
  opacity: 0.9;
  margin-bottom: 8rpx;
  display: block;
}

.balance-amount {
  font-size: 72rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  display: block;
}

.balance-desc {
  font-size: 28rpx;
  opacity: 0.8;
  display: block;
}

.wallet-icon {
  text-align: center;
}

.wallet-emoji {
  font-size: 80rpx;
  opacity: 0.8;
  margin-bottom: 16rpx;
  display: block;
}

.wallet-text {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 充值金额选择 */
.amount-section {
  background: #ffffff;
  margin: 32rpx 16rpx;
  border-radius: 32rpx;
  padding: 40rpx 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 32rpx;
  display: block;
}

.amount-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

.amount-btn {
  background: #f9fafb;
  border: 2rpx solid transparent;
  border-radius: 24rpx;
  padding: 24rpx 12rpx;
  text-align: center;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120rpx;
  box-sizing: border-box;
  flex: 0 0 calc(45% - 6rpx);
  max-width: calc(45% - 6rpx);
  overflow: hidden;
}

.amount-btn.selected {
  background: #1f2937;
  border-color: #1f2937;
}

.amount-btn.selected .amount-value,
.amount-btn.selected .amount-desc {
  color: #ffffff;
}

.amount-value {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8rpx;
  display: block;
}

.amount-desc {
  font-size: 24rpx;
  color: #6b7280;
}

/* 自定义金额 */
.custom-amount {
  margin-top: 32rpx;
  padding: 0 8rpx;
}

.custom-label {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20rpx;
  display: block;
}

.custom-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 24rpx;
  transition: all 0.3s ease;
  height: 100rpx;
}

.custom-input-wrapper:focus-within {
  border-color: #1f2937;
  background: #ffffff;
  box-shadow: 0 0 0 4rpx rgba(31, 41, 55, 0.1);
}

.currency-symbol {
  padding: 0 16rpx 0 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex-shrink: 0;
  height: 100rpx;
  line-height: 100rpx;
}

.custom-input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 0 24rpx 0 0;
  font-size: 32rpx;
  color: #1f2937;
  font-weight: 500;
  box-sizing: border-box;
  min-width: 0;
  height: 100rpx;
  line-height: 100rpx;
  text-align: left;
  vertical-align: middle;
}

.custom-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.amount-tip {
  font-size: 26rpx;
  color: #6b7280;
  margin-top: 16rpx;
  display: block;
  line-height: 1.4;
  padding: 0 8rpx;
}

/* 支付方式选择 */
.payment-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 32rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.payment-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.payment-method {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border: 4rpx solid #e5e7eb;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.payment-method.selected {
  border-color: #1f2937;
  background: #f9fafb;
}

.payment-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.payment-icon-wrapper {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-icon-wrapper.green {
  background: #dcfce7;
}

.payment-icon-wrapper.blue {
  background: #dbeafe;
}

.payment-icon {
  font-size: 40rpx;
}

.payment-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.payment-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.payment-desc {
  font-size: 28rpx;
  color: #6b7280;
}

.payment-radio {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-outer {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid #d1d5db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-inner {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background: transparent;
}

.radio-inner.selected {
  background: #1f2937;
}

/* 充值优惠活动 */
.promo-section {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  margin: 32rpx 16rpx 80rpx 16rpx;
  border-radius: 32rpx;
  padding: 40rpx 32rpx 48rpx 32rpx;
  border: 2rpx solid #fbbf24;
  box-sizing: border-box;
  overflow: visible;
  min-height: auto;
}

.promo-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.promo-icon {
  font-size: 40rpx;
}

.promo-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #92400e;
}

.promo-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.promo-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  min-height: 48rpx;
  padding: 8rpx 0;
  box-sizing: border-box;
}

.promo-check {
  font-size: 28rpx;
  color: #d97706;
  font-weight: 600;
  flex-shrink: 0;
  width: 32rpx;
  text-align: center;
  line-height: 1.2;
}

.promo-text {
  font-size: 28rpx;
  color: #92400e;
  line-height: 1.5;
  flex: 1;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 充值记录入口 */
.history-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
}

.history-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.history-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  background: #f3f4f6;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-icon {
  font-size: 32rpx;
}

.history-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
}

.history-arrow {
  font-size: 32rpx;
  color: #9ca3af;
}

/* 安全提示 */
.security-section {
  background: #eff6ff;
  border: 2rpx solid #bfdbfe;
  margin: 32rpx;
  border-radius: 32rpx;
  padding: 32rpx;
}

.security-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 16rpx;
}

.security-icon {
  font-size: 32rpx;
}

.security-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e40af;
}

.security-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.security-item {
  font-size: 28rpx;
  color: #1e40af;
  line-height: 1.5;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-top: 2rpx solid #e5e7eb;
  padding: 48rpx;
  z-index: 100;
}

.bottom-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.bottom-label {
  font-size: 32rpx;
  color: #6b7280;
}

.bottom-amount {
  font-size: 48rpx;
  font-weight: 700;
  color: #1f2937;
}

.confirm-btn {
  width: 100%;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: #ffffff;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 0;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.confirm-btn:not(.disabled):active {
  transform: translateY(-4rpx);
  box-shadow: 0 20rpx 50rpx rgba(31, 41, 55, 0.3);
}

.confirm-btn.disabled {
  background: #d1d5db;
  color: #9ca3af;
  transform: none;
  box-shadow: none;
}
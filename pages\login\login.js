const app = getApp()
const auth = require('../../utils/auth.js')
const { mockUser } = require('../../utils/mockData.js')

Page({
  data: {
    // 表单数据
    agreeTerms: true,

    // 按钮状态
    wxLoading: false,
    phoneLoading: false,

    // 全局加载
    isLoading: false,
    loadingText: '登录中...'
  },

  onLoad(options) {
    console.log('登录页面加载，参数：', options)
    
    // 检查是否已经登录
    if (auth.checkLoginStatus()) {
      console.log('用户已登录，跳转首页')
      this.redirectToHome()
      return
    }
    
    // 保存重定向参数
    if (options.redirect) {
      wx.setStorageSync('redirectUrl', decodeURIComponent(options.redirect))
    }
  },

  onShow() {
    // 每次显示页面时检查登录状态
    if (auth.checkLoginStatus()) {
      this.redirectToHome()
    }
  },

  /**
   * 微信登录处理
   */
  async handleWechatLogin() {
    if (!this.data.agreeTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    this.setData({ wxLoading: true })

    try {
      // 获取微信登录code
      const loginResult = await this.getWechatLoginCode()
      console.log('微信登录code：', loginResult.code)

      // 模拟用户信息（新版本不再需要获取用户信息）
      const mockUserInfo = {
        nickName: '逍遥用户',
        avatarUrl: '',
        gender: 0,
        country: '',
        province: '',
        city: '',
        language: 'zh_CN'
      }

      // 模拟登录API调用
      const loginData = await this.callLoginAPI({
        type: 'wechat',
        code: loginResult.code,
        userInfo: mockUserInfo
      })

      // 处理登录成功
      await this.handleLoginSuccess(loginData)

    } catch (error) {
      console.error('微信登录失败：', error)

      if (error.errMsg && error.errMsg.includes('login:fail')) {
        wx.showToast({
          title: '微信登录失败',
          icon: 'none'
        })
      } else {
        wx.showToast({
          title: error.message || '登录失败，请重试',
          icon: 'error'
        })
      }
    } finally {
      this.setData({ wxLoading: false })
    }
  },

  /**
   * 微信手机号授权登录处理
   */
  async handlePhoneAuthLogin(e) {
    console.log('手机号授权事件触发，详细信息：', e.detail)

    if (!this.data.agreeTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    // 检查用户是否授权
    if (e.detail.errMsg !== 'getPhoneNumber:ok') {
      console.log('用户授权失败，错误信息：', e.detail.errMsg)

      // 处理不同类型的错误
      if (e.detail.errMsg === 'getPhoneNumber:fail no permission') {
        // 没有权限 - 小程序未认证或个人开发者
        wx.showModal({
          title: '功能限制',
          content: '手机号授权功能需要企业认证小程序才能使用。是否使用模拟数据登录？（仅开发环境）',
          confirmText: '模拟登录',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              this.simulatePhoneLogin()
            }
          }
        })
      } else if (e.detail.errMsg === 'getPhoneNumber:fail user deny') {
        // 用户拒绝授权
        wx.showModal({
          title: '授权提示',
          content: '需要手机号授权才能登录。是否使用模拟数据登录？（仅开发环境）',
          confirmText: '模拟登录',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              this.simulatePhoneLogin()
            }
          }
        })
      } else if (e.detail.errMsg.includes('1400001')) {
        // 额度不足
        wx.showModal({
          title: '额度不足',
          content: '手机号验证次数已达上限。是否使用模拟数据登录？（仅开发环境）',
          confirmText: '模拟登录',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              this.simulatePhoneLogin()
            }
          }
        })
      } else {
        // 其他错误
        wx.showModal({
          title: '授权失败',
          content: `授权失败：${e.detail.errMsg}。是否使用模拟数据登录？（仅开发环境）`,
          confirmText: '模拟登录',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              this.simulatePhoneLogin()
            }
          }
        })
      }
      return
    }

    this.setData({ phoneLoading: true })

    try {
      // 获取微信登录code
      const loginResult = await this.getWechatLoginCode()
      console.log('微信登录code：', loginResult.code)

      // 获取手机号授权数据
      const { encryptedData, iv } = e.detail
      console.log('手机号授权数据：', { encryptedData, iv })

      // 模拟登录API调用
      const loginData = await this.callLoginAPI({
        type: 'phone_auth',
        code: loginResult.code,
        encryptedData: encryptedData,
        iv: iv
      })

      // 处理登录成功
      await this.handleLoginSuccess(loginData)

    } catch (error) {
      console.error('手机号授权登录失败：', error)
      wx.showToast({
        title: error.message || '登录失败，请重试',
        icon: 'error'
      })
    } finally {
      this.setData({ phoneLoading: false })
    }
  },

  /**
   * 模拟手机号登录（开发环境使用）
   */
  async simulatePhoneLogin() {
    this.setData({ phoneLoading: true })

    try {
      // 获取微信登录code
      const loginResult = await this.getWechatLoginCode()
      console.log('微信登录code：', loginResult.code)

      // 模拟手机号授权数据
      const mockPhoneData = {
        encryptedData: 'mock_encrypted_data',
        iv: 'mock_iv'
      }

      // 模拟登录API调用
      const loginData = await this.callLoginAPI({
        type: 'phone_auth',
        code: loginResult.code,
        encryptedData: mockPhoneData.encryptedData,
        iv: mockPhoneData.iv
      })

      // 处理登录成功
      await this.handleLoginSuccess(loginData)

    } catch (error) {
      console.error('模拟手机号登录失败：', error)
      wx.showToast({
        title: error.message || '登录失败，请重试',
        icon: 'error'
      })
    } finally {
      this.setData({ phoneLoading: false })
    }
  },

  /**
   * 获取微信登录code
   */
  getWechatLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  },

  /**
   * 获取用户信息（已废弃，使用模拟数据）
   * 注意：getUserProfile API已被微信废弃，现在使用头像昵称填写能力
   */
  // getUserProfile() {
  //   return new Promise((resolve, reject) => {
  //     wx.getUserProfile({
  //       desc: '用于完善用户资料',
  //       success: resolve,
  //       fail: reject
  //     })
  //   })
  // },





  /**
   * 调用登录API
   */
  async callLoginAPI(params) {
    console.log('登录API参数：', params)

    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 返回模拟用户数据
    const userData = {
      ...mockUser,
      loginType: params.type,
      loginTime: new Date().toISOString(),
      token: 'mock_token_' + Date.now()
    }

    // 如果是微信登录，更新用户信息
    if (params.type === 'wechat' && params.userInfo) {
      userData.nickname = params.userInfo.nickName
      userData.avatar = params.userInfo.avatarUrl
    }

    // 如果是手机号授权登录，模拟解密手机号
    if (params.type === 'phone_auth' && params.encryptedData && params.iv) {
      // 模拟解密后的手机号（实际应该在后端解密）
      userData.phone = '138****8888'
      userData.nickname = '手机用户' + userData.phone.slice(-4)
      console.log('模拟手机号授权登录成功，手机号：', userData.phone)
    }

    return userData
  },

  /**
   * 处理登录成功
   */
  async handleLoginSuccess(userData) {
    console.log('登录成功，用户数据：', userData)

    this.setData({
      isLoading: true,
      loadingText: '登录成功，正在跳转...'
    })

    try {
      // 使用认证工具处理登录成功
      const success = auth.handleLoginSuccess(userData, {
        showWelcome: false // 我们在这里自己处理欢迎信息
      })

      if (success) {
        // 显示欢迎信息
        wx.showToast({
          title: `欢迎，${userData.nickname}`,
          icon: 'success',
          duration: 2000
        })

        // 延迟跳转，让用户看到欢迎信息
        setTimeout(() => {
          auth.handleLoginRedirect()
        }, 2000)
      }

    } catch (error) {
      console.error('登录成功处理失败：', error)
      wx.showToast({
        title: '登录处理失败',
        icon: 'error'
      })
    } finally {
      this.setData({ isLoading: false })
    }
  },

  /**
   * 跳转到首页
   */
  redirectToHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  /**
   * 表单输入处理
   */
  onAgreeChange(e) {
    this.setData({
      agreeTerms: e.detail.value.length > 0
    })
  },

  /**
   * 查看协议
   */
  viewTerms() {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议的具体内容...',
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  viewPrivacy() {
    wx.showModal({
      title: '隐私政策',
      content: '这里是隐私政策的具体内容...',
      showCancel: false,
      confirmText: '我知道了'
    })
  },


}) 
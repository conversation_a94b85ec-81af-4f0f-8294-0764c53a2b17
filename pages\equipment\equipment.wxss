/* pages/equipment/equipment.wxss */

.equipment-container {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  padding-bottom: 32rpx;
}

/* 顶部导航 */
.header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 32rpx 48rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border-bottom: 1rpx solid rgba(148, 163, 184, 0.1);
  border-top: 2rpx solid rgba(31, 41, 55, 0.6);
  position: relative;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg,
    rgba(31, 41, 55, 0) 0%,
    rgba(31, 41, 55, 0.8) 20%,
    rgba(31, 41, 55, 1) 50%,
    rgba(31, 41, 55, 0.8) 80%,
    rgba(31, 41, 55, 0) 100%
  );
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #1f2937;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.action-icon {
  font-size: 40rpx;
  color: #64748b;
  padding: 16rpx;
  border-radius: 50%;
  background: rgba(148, 163, 184, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

.action-icon:active {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  transform: scale(0.95);
  color: #475569;
}

/* 筛选栏 */
.filter-bar {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 24rpx 48rpx;
  border-bottom: 1rpx solid rgba(148, 163, 184, 0.1);
}

.filter-scroll {
  white-space: nowrap;
}

.filter-list {
  display: flex;
  gap: 24rpx;
}

.filter-item {
  padding: 16rpx 32rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  color: #6b7280;
  background: #ffffff;
  border: 2rpx solid #e5e7eb;
  transition: all 0.2s ease;
  flex-shrink: 0;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.filter-item.active {
  background: #1f2937;
  color: #ffffff;
  border-color: #1f2937;
}

.filter-item:hover {
  background: #e5e7eb;
}

.filter-item.active:hover {
  background: #059669;
}

.sort-btn {
  width: 80rpx;
  height: 80rpx;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sort-btn:hover {
  background: #e5e7eb;
  transform: scale(1.05);
}

.sort-icon {
  font-size: 32rpx;
  color: #6b7280;
}

/* 设备列表 */
.equipment-list {
  padding: 24rpx 48rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.equipment-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 32rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.08), 0 3rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(148, 163, 184, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.equipment-card:active {
  transform: translateY(-6rpx) scale(1.01);
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.12), 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  border-color: rgba(31, 41, 55, 0.2);
}

.equipment-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #1f2937 0%, #374151 50%, #4b5563 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.equipment-card:active::before {
  opacity: 1;
}

.card-content {
  display: flex;
  padding: 24rpx;
  gap: 20rpx;
  align-items: flex-start;
}

.equipment-image-section {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.equipment-image {
  width: 180rpx;
  height: 135rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.equipment-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
  gap: 12rpx;
}

.equipment-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 12rpx;
}

.equipment-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.3;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.equipment-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  flex-shrink: 0;
  white-space: nowrap;
}

.equipment-status.available {
  background: #dcfce7;
  color: #166534;
}

.equipment-status.unavailable {
  background: #fef3c7;
  color: #92400e;
}

.equipment-desc {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.4;
  word-wrap: break-word;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.price-section {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(31, 41, 55, 0.2);
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
  justify-content: center;
}

.button-section {
  display: flex;
  justify-content: flex-end;
  margin-top: auto;
}

.price-amount {
  font-size: 28rpx;
  font-weight: 700;
  color: #ffffff;
  line-height: 1;
}

.price-unit {
  font-size: 20rpx;
  color: #e2e8f0;
  line-height: 1;
}

.rent-btn {
  background: #1f2937;
  color: #ffffff;
  padding: 12rpx 32rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.rent-btn.disabled {
  background: #d1d5db;
  color: #6b7280;
}

.equipment-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.rating-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-star {
  font-size: 24rpx;
  color: #fbbf24;
}

.rating-text {
  font-size: 22rpx;
  color: #6b7280;
}

.availability-info {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.availability-icon {
  font-size: 22rpx;
  color: #6b7280;
}

.availability-text {
  font-size: 22rpx;
  color: #6b7280;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 64rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 64rpx;
  text-align: center;
}

.loading-icon {
  font-size: 80rpx;
  margin-bottom: 32rpx;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 32rpx;
  color: #6b7280;
}

/* 按钮样式 */
.btn {
  padding: 24rpx 48rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #10a37f;
  color: #ffffff;
}

.btn-primary:hover {
  background: #059669;
  transform: translateY(-2rpx);
}

/* 排序弹窗 */
.sort-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.sort-modal-content {
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 80vh;
  padding: 32rpx;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.sort-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid #f3f4f6;
}

.sort-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.sort-close {
  width: 64rpx;
  height: 64rpx;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sort-close:hover {
  background: #e5e7eb;
  color: #1f2937;
}

.sort-options {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.sort-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sort-option:hover {
  background: #f9fafb;
}

.sort-option.active {
  background: rgba(16, 163, 127, 0.1);
}

.sort-option-text {
  font-size: 32rpx;
  color: #1f2937;
}

.sort-option.active .sort-option-text {
  color: #10a37f;
  font-weight: 500;
}

.sort-option-icon {
  font-size: 32rpx;
  color: #10a37f;
  font-weight: 600;
}
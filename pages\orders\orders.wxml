<!--pages/orders/orders.wxml-->
<view class="orders-container">
  <!-- 顶部导航 -->
  <view class="header">
    <text class="header-title">我的订单</text>
  </view>

  <!-- 状态筛选栏 -->
  <view class="status-bar">
    <scroll-view class="status-scroll" scroll-x="true">
      <view class="status-list">
        <view
          class="status-item {{currentTab === item.key ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="{{item.key}}"
          wx:for="{{tabs}}"
          wx:key="key"
          wx:for-item="item"
        >
          {{item.name}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 订单列表 -->
  <view class="orders-list" wx:if="{{filteredOrders.length > 0}}">
    <view
      class="order-card"
      data-status="{{item.status}}"
      wx:for="{{filteredOrders}}"
      wx:key="id"
      bindtap="viewOrderDetail"
      data-order-id="{{item.id}}"
    >
      <!-- 订单头部 -->
      <view class="order-header">
        <view class="header-left">
          <text class="equipment-name">{{item.equipmentName}}</text>
          <text class="order-number">订单号: {{item.id}}</text>
        </view>
        <view class="order-status {{item.status}}">
          <text class="status-dot" wx:if="{{item.status === 'ongoing'}}">●</text>
          {{item.statusText}}
        </view>
      </view>

      <!-- 订单内容 -->
      <view class="order-content">
        <view class="equipment-section">
          <image
            class="equipment-image"
            src="{{item.equipmentImage || 'http://iph.href.lu/300x200'}}"
            mode="aspectFill"
          />
          <view class="equipment-details">
            <view class="detail-row">
              <text class="detail-icon">📍</text>
              <text class="detail-text">{{item.locationName}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-icon">🕐</text>
              <text class="detail-text">{{item.startTime}} ({{item.duration}}小时)</text>
            </view>
            <view class="detail-row">
              <text class="detail-icon">💰</text>
              <text class="detail-text">¥{{item.totalAmount}}</text>
            </view>
          </view>
        </view>

        <!-- 进行中订单的进度条 -->
        <view class="progress-section" wx:if="{{item.status === 'ongoing'}}">
          <view class="progress-info">
            <text class="progress-label">
              <text class="progress-icon">▶️</text>正在操控中
            </text>
            <text class="progress-time">剩余时间: {{item.remainingTime || '1小时15分'}}</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.progress || 35}}%"></view>
          </view>
        </view>

        <!-- 已完成订单的统计 -->
        <view class="stats-section" wx:if="{{item.status === 'completed'}}">
          <view class="stats-info">
            <text class="stats-item">
              <text class="stats-icon">📷</text> 已拍摄 {{item.photoCount || 0}}张照片
            </text>
            <text class="stats-item">
              <text class="stats-icon">🎥</text> 已录制 {{item.videoCount || 0}}段视频
            </text>
          </view>
        </view>

        <!-- 已取消订单的原因 -->
        <view class="cancel-section" wx:if="{{item.status === 'cancelled'}}">
          <view class="cancel-info">
            <text class="cancel-icon">⚠️</text>
            <text class="cancel-reason">{{item.cancelReason || '因天气原因取消，已全额退款'}}</text>
          </view>
        </view>
      </view>

      <!-- 订单操作 -->
      <view class="order-actions">
        <!-- 进行中订单操作 -->
        <block wx:if="{{item.status === 'ongoing'}}">
          <button
            class="action-btn primary-full"
            catchtap="continueControl"
            data-order="{{item}}"
          >
            继续操控
          </button>
          <button
            class="action-btn secondary-small"
            catchtap="endEarly"
            data-order-id="{{item.id}}"
          >
            提前结束
          </button>
        </block>

        <!-- 已完成订单操作 -->
        <block wx:if="{{item.status === 'completed'}}">
          <button
            class="action-btn primary-full"
            catchtap="viewGallery"
            data-order-id="{{item.id}}"
          >
            查看作品
          </button>
          <button
            class="action-btn secondary-small rate"
            catchtap="rateOrder"
            data-order-id="{{item.id}}"
            wx:if="{{!item.rated}}"
          >
            评价
          </button>
          <button
            class="action-btn secondary-small disabled"
            wx:if="{{item.rated}}"
          >
            已评价
          </button>
          <button
            class="action-btn secondary-small reorder"
            catchtap="reorder"
            data-order-id="{{item.id}}"
          >
            再次预订
          </button>
        </block>

        <!-- 已取消订单操作 -->
        <block wx:if="{{item.status === 'cancelled'}}">
          <button
            class="action-btn primary-full"
            catchtap="reorder"
            data-order-id="{{item.id}}"
          >
            重新预订
          </button>
        </block>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredOrders.length === 0 && !loading}}">
    <text class="empty-icon">📋</text>
    <text class="empty-title">暂无订单</text>
    <text class="empty-desc">当前状态下没有找到订单</text>
    <button class="btn btn-primary" bindtap="goToEquipment">去租赁设备</button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-icon">⏳</text>
    <text class="loading-text">加载中...</text>
  </view>
</view>
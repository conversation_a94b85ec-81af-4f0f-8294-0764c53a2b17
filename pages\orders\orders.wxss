/* pages/orders/orders.wxss */

.orders-container {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  padding-bottom: 32rpx;
}

/* 顶部导航 */
.header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 32rpx 48rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border-bottom: 1rpx solid rgba(148, 163, 184, 0.1);
  border-top: 2rpx solid rgba(31, 41, 55, 0.6);
  position: relative;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg,
    rgba(31, 41, 55, 0) 0%,
    rgba(31, 41, 55, 0.8) 20%,
    rgba(31, 41, 55, 1) 50%,
    rgba(31, 41, 55, 0.8) 80%,
    rgba(31, 41, 55, 0) 100%
  );
}

.header-title {
  font-size: 48rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4f46e5 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 状态筛选栏 */
.status-bar {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(148, 163, 184, 0.1);
}

.status-scroll {
  white-space: nowrap;
}

.status-list {
  display: flex;
  padding: 0 48rpx;
  gap: 24rpx;
}

.status-item {
  padding: 16rpx 32rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #6b7280;
  background: transparent;
  border: 2rpx solid #e5e7eb;
  transition: all 0.2s ease;
  flex-shrink: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1.2;
  min-height: 56rpx;
  box-sizing: border-box;
}

.status-item.active {
  background: #1f2937;
  color: #ffffff;
  border-color: #1f2937;
  font-weight: 500;
}

/* 订单列表 */
.orders-list {
  padding: 24rpx 32rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.order-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.08), 0 3rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(148, 163, 184, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
}

.order-card:hover {
  transform: translateY(-6rpx) scale(1.01);
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.12), 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  border-color: rgba(31, 41, 55, 0.2);
}

.order-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #1f2937 0%, #374151 50%, #4b5563 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.order-card:hover::before {
  opacity: 1;
}

/* 进行中订单特殊边框 */
.order-card[data-status="ongoing"] {
  border-left: 8rpx solid #10b981;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 32rpx 32rpx 0;
  margin-bottom: 20rpx;
}

.header-left {
  flex: 1;
}

.equipment-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
  display: block;
}

.order-number {
  font-size: 28rpx;
  color: #6b7280;
}

.order-status {
  padding: 8rpx 24rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-dot {
  font-size: 16rpx;
}

.order-status.ongoing {
  background: linear-gradient(135deg, #10b981, #059669);
  color: #ffffff;
}

.order-status.completed {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: #ffffff;
}

.order-status.cancelled {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: #ffffff;
}

/* 订单内容 */
.order-content {
  padding: 0 32rpx 24rpx;
}

.equipment-section {
  display: flex;
  align-items: center;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.equipment-image {
  width: 128rpx;
  height: 96rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.equipment-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.detail-icon {
  font-size: 24rpx;
  width: 32rpx;
}

.detail-text {
  font-size: 28rpx;
  color: #6b7280;
}

.price-text {
  font-weight: 600;
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 进度条区域 */
.progress-section {
  background: #f0fdf4;
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-label {
  font-size: 28rpx;
  color: #15803d;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.progress-icon {
  font-size: 24rpx;
}

.progress-time {
  font-size: 28rpx;
  color: #16a34a;
}

.progress-bar {
  height: 16rpx;
  background: #bbf7d0;
  border-radius: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #16a34a;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

/* 统计区域 */
.stats-section {
  background: #f9fafb;
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.stats-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-item {
  font-size: 28rpx;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stats-icon {
  font-size: 24rpx;
}

/* 取消区域 */
.cancel-section {
  background: #fef2f2;
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.cancel-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.cancel-icon {
  font-size: 24rpx;
}

.cancel-reason {
  font-size: 28rpx;
  color: #dc2626;
}

/* 订单操作 */
.order-actions {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 32rpx 32rpx;
}

.action-btn {
  height: 80rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.action-btn.primary-full {
  flex: 1;
  background: #1f2937;
  color: #ffffff;
  box-shadow: 0 4rpx 15rpx rgba(31, 41, 55, 0.3);
}

.action-btn.primary-full:hover {
  background: #111827;
  transform: translateY(-2rpx);
}

.action-btn.secondary-small {
  padding: 0 32rpx;
  background: #f3f4f6;
  color: #6b7280;
}

.action-btn.secondary-small:hover {
  background: #e5e7eb;
  color: #1f2937;
}

.action-btn.disabled {
  background: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
}

/* 已完成订单的特殊按钮样式 */
.order-card[data-status="completed"] .action-btn.primary-full {
  min-width: 160rpx;
  padding: 0 24rpx;
  white-space: nowrap;
}

/* 已完成订单的评价和再次预订按钮 */
.order-card[data-status="completed"] .action-btn.rate,
.order-card[data-status="completed"] .action-btn.reorder {
  min-width: 160rpx;
  padding: 0 24rpx;
  white-space: nowrap;
}

/* 黄色评价按钮 */
.action-btn.rate {
  background: #f59e0b;
  color: #ffffff;
}

.action-btn.rate:hover {
  background: #d97706;
}

/* 蓝色再次预订按钮 */
.action-btn.reorder {
  background: #2563eb;
  color: #ffffff;
}

.action-btn.reorder:hover {
  background: #1d4ed8;
}

/* 空状态和加载状态 */
.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 64rpx;
  text-align: center;
}

.empty-icon,
.loading-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title,
.loading-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.btn {
  padding: 24rpx 48rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.btn-primary {
  background: #1f2937;
  color: #ffffff;
  box-shadow: 0 4rpx 15rpx rgba(31, 41, 55, 0.3);
}

.btn-primary:hover {
  background: #111827;
  transform: translateY(-2rpx);
}
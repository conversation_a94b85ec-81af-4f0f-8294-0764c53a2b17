// pages/home/<USER>
const app = getApp()
const auth = require('../../utils/auth.js')
const request = require('../../utils/request.js')
const { mockUser, mockEquipment, mockLocations, mockOrders } = require('../../utils/mockData.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,
    userStats: {
      totalOrders: 0,
      totalFlightTime: 0,
      worksCount: 0,
      creditScore: 0
    },
    
    // 推荐数据
    hotEquipment: [],
    hotLocations: [],
    recentOrders: [],
    
    // 搜索相关
    showSearch: false,
    searchKeyword: '',
    searchSuggestions: ['DJI Air 3', '书圣故里', 'DJI Mini 4 Pro', '鲁迅故里', '柯岩风景区', '东湖风景区'],
    
    // 页面状态
    loading: false,
    refreshing: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('首页加载')
    this.checkAuth()
    this.initPageData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('首页显示')
    this.refreshUserInfo()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新')
    this.refreshPageData()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 检查认证状态
   */
  checkAuth() {
    if (!auth.checkLoginStatus()) {
      console.log('用户未登录，跳转登录页')
      auth.redirectToLogin('/pages/home/<USER>')
      return false
    }
    return true
  },

  /**
   * 初始化页面数据
   */
  async initPageData() {
    if (!this.checkAuth()) return

    this.setData({ loading: true })

    try {
      // 并行获取数据
      await Promise.all([
        this.loadUserInfo(),
        this.loadHotEquipment(),
        this.loadHotLocations(),
        this.loadRecentOrders(),
        this.loadUserStats()
      ])

      console.log('首页数据加载完成')
    } catch (error) {
      console.error('首页数据加载失败：', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 刷新页面数据
   */
  async refreshPageData() {
    this.setData({ refreshing: true })
    
    try {
      await this.initPageData()
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('刷新失败：', error)
    } finally {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      const userInfo = auth.getCurrentUser()
      if (userInfo) {
        this.setData({ userInfo })
        console.log('用户信息已加载：', userInfo.nickname)
      }
    } catch (error) {
      console.error('加载用户信息失败：', error)
    }
  },

  /**
   * 刷新用户信息
   */
  async refreshUserInfo() {
    try {
      // 模拟从服务器获取最新用户信息
      const updatedUser = await request.get('/user/profile')
      auth.updateUserInfo(updatedUser)
      this.setData({ userInfo: updatedUser })
    } catch (error) {
      console.error('刷新用户信息失败：', error)
      // 使用本地缓存的用户信息
      this.loadUserInfo()
    }
  },

  /**
   * 加载热门设备
   */
  async loadHotEquipment() {
    try {
      // 取前4个可用设备，添加描述信息
      const hotEquipment = mockEquipment
        .filter(item => item.available)
        .slice(0, 4)
        .map(item => ({
          ...item,
          description: item.description || `${item.features?.[0] || '专业航拍'} · ${item.batteryLife || '30'}分钟续航`,
          image: item.image || 'http://iph.href.lu/300x200'
        }))

      this.setData({ hotEquipment })
      console.log('热门设备已加载：', hotEquipment.length, '个')
    } catch (error) {
      console.error('加载热门设备失败：', error)
    }
  },

  /**
   * 加载热门地点
   */
  async loadHotLocations() {
    try {
      // 取前2个推荐地点，添加分类信息
      const hotLocations = mockLocations
        .filter(item => item.recommended)
        .slice(0, 2)
        .map(item => ({
          ...item,
          category: item.category || '风景名胜',
          image: item.images?.[0] || 'http://iph.href.lu/128x128',
          reviewCount: item.reviewCount || Math.floor(Math.random() * 500) + 100
        }))

      this.setData({ hotLocations })
      console.log('热门地点已加载：', hotLocations.length, '个')
    } catch (error) {
      console.error('加载热门地点失败：', error)
    }
  },

  /**
   * 加载最近订单
   */
  async loadRecentOrders() {
    try {
      // 取最近2个订单
      const recentOrders = mockOrders
        .slice(0, 2)
        .map(order => ({
          ...order,
          startTime: this.formatDateTime(order.startTime)
        }))
      
      this.setData({ recentOrders })
      console.log('最近订单已加载：', recentOrders.length, '个')
    } catch (error) {
      console.error('加载最近订单失败：', error)
    }
  },

  /**
   * 加载用户统计
   */
  async loadUserStats() {
    try {
      const userInfo = auth.getCurrentUser()
      if (userInfo) {
        const userStats = {
          totalOrders: userInfo.totalOrders || 25,
          totalFlightTime: userInfo.totalFlightTime || 120,
          worksCount: 12, // 从作品集获取
          creditScore: userInfo.creditScore || 850
        }
        
        this.setData({ userStats })
        console.log('用户统计已加载')
      }
    } catch (error) {
      console.error('加载用户统计失败：', error)
    }
  },

  /**
   * 搜索相关方法
   */
  showSearchModal() {
    this.setData({ showSearch: true })
  },

  hideSearchModal() {
    this.setData({ 
      showSearch: false,
      searchKeyword: ''
    })
  },

  stopPropagation() {
    // 阻止事件冒泡
  },

  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value })
  },

  selectSuggestion(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({ searchKeyword: keyword })
    this.performSearch()
  },

  performSearch() {
    const keyword = this.data.searchKeyword.trim()
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      })
      return
    }

    console.log('搜索关键词：', keyword)
    this.hideSearchModal()

    // 根据关键词类型跳转到不同页面
    if (this.isEquipmentKeyword(keyword)) {
      wx.switchTab({
        url: '/pages/equipment/equipment',
        success: () => {
          // 通过EventChannel传递搜索关键词
          const eventChannel = this.getOpenerEventChannel()
          if (eventChannel) {
            eventChannel.emit('search', { keyword })
          }
        }
      })
    } else if (this.isLocationKeyword(keyword)) {
      wx.navigateTo({
        url: `/pages/location/location?search=${encodeURIComponent(keyword)}`
      })
    } else {
      // 综合搜索
      wx.switchTab({
        url: '/pages/equipment/equipment'
      })
    }
  },

  isEquipmentKeyword(keyword) {
    const equipmentKeywords = ['DJI', 'Air', 'Mini', 'Mavic', 'FPV', '无人机', '设备']
    return equipmentKeywords.some(key => keyword.includes(key))
  },

  isLocationKeyword(keyword) {
    const locationKeywords = ['故里', '风景区', '古镇', '湖', '地点', '景点']
    return locationKeywords.some(key => keyword.includes(key))
  },

  /**
   * 页面导航方法
   */
  quickRent() {
    wx.switchTab({
      url: '/pages/equipment/equipment'
    })
  },

  findLocation() {
    wx.navigateTo({
      url: '/pages/location/location'
    })
  },

  goToProfile() {
    wx.switchTab({
      url: '/pages/profile/profile'
    })
  },

  goToRecharge() {
    wx.navigateTo({
      url: '/pages/recharge/recharge'
    })
  },

  goToEquipment() {
    wx.switchTab({
      url: '/pages/equipment/equipment'
    })
  },

  goToLocation() {
    wx.navigateTo({
      url: '/pages/location/location'
    })
  },

  goToOrders() {
    wx.switchTab({
      url: '/pages/orders/orders'
    })
  },

  goToGallery() {
    wx.navigateTo({
      url: '/pages/gallery/gallery'
    })
  },

  goToEquipmentDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/equipment-detail/equipment-detail?id=${id}`
    })
  },

  goToLocationDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/location/location?highlight=${id}`
    })
  },

  goToOrderDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/orders/orders?highlight=${id}`
    })
  },

  /**
   * 新增方法 - 按照高保真设计
   */
  rentEquipment(e) {
    const id = e.currentTarget.dataset.id
    console.log('租用设备：', id)
    wx.navigateTo({
      url: `/pages/equipment-detail/equipment-detail?id=${id}`
    })
  },

  showNotifications() {
    wx.showToast({
      title: '暂无新通知',
      icon: 'none'
    })
  },

  showCustomerService() {
    wx.showModal({
      title: '客服支持',
      content: '请拨打客服热线：************',
      confirmText: '拨打',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '4001234567'
          })
        }
      }
    })
  },

  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 工具方法
   */
  formatDateTime(dateTimeStr) {
    const date = new Date(dateTimeStr)
    const now = new Date()
    const diff = now - date

    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 24小时内
      return `${Math.floor(diff / 3600000)}小时前`
    } else {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  }
})
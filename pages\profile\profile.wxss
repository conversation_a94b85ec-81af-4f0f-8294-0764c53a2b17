/* pages/profile/profile.wxss */

.profile-container {
  background-color: #f9fafb;
  min-height: 100vh;
  padding-bottom: 32rpx;
}

/* 用户信息卡片 */
.user-info-card {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  margin: 32rpx;
  border-radius: 32rpx;
  padding: 48rpx;
  color: #ffffff;
}

.user-content {
  display: flex;
  align-items: center;
  gap: 32rpx;
  position: relative;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-icon {
  font-size: 48rpx;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-name {
  font-size: 40rpx;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.2;
}

.user-id {
  font-size: 28rpx;
  opacity: 0.8;
  color: #ffffff;
  line-height: 1.2;
}

.user-badges {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-top: 8rpx;
}

.level-badge {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  padding: 6rpx 24rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #ffffff;
}

.crown-icon {
  font-size: 20rpx;
}

.flight-time {
  font-size: 24rpx;
  opacity: 0.6;
  color: #ffffff;
}

.edit-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  font-size: 32rpx;
  color: #ffffff;
  opacity: 0.8;
}

/* 余额卡片 */
.balance-card {
  background: linear-gradient(135deg, #444746 0%, #50645e 100%);
  margin: 32rpx;
  border-radius: 32rpx;
  padding: 40rpx;
  color: #ffffff;
  position: relative;
}

.balance-content {
  margin-bottom: 32rpx;
}

.balance-info {
  width: 100%;
}

.balance-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
}

.balance-amount {
  font-size: 72rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  display: block;
}

.balance-desc {
  font-size: 28rpx;
  opacity: 0.8;
  display: block;
}

.wallet-icon {
  position: absolute;
  top: 0rpx;
  right: 40rpx;
  text-align: center;
}

.wallet-emoji {
  font-size: 60rpx;
  margin-bottom: 8rpx;
  opacity: 0.8;
  display: block;
}

.wallet-text {
  font-size: 24rpx;
  opacity: 0.8;
}

.balance-actions {
  display: flex;
  gap: 24rpx;
}

.balance-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 24rpx;
  padding: 16rpx 0;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.balance-btn.secondary {
  flex: 0 0 auto;
  padding: 16rpx 32rpx;
}

.btn-icon {
  font-size: 24rpx;
}

/* 统计信息 */
.stats-section {
  margin: 0 32rpx 32rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(148, 163, 184, 0.1);
  position: relative;
  overflow: hidden;
}

.stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg,
    rgba(31, 41, 55, 0) 0%,
    rgba(31, 41, 55, 0.3) 20%,
    rgba(31, 41, 55, 0.6) 50%,
    rgba(31, 41, 55, 0.3) 80%,
    rgba(31, 41, 55, 0) 100%
  );
}

.stats-grid {
  display: flex;
  gap: 24rpx;
}

.stats-item {
  flex: 1;
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
  border-radius: 24rpx;
  padding: 28rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(148, 163, 184, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stats-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg,
    rgba(79, 70, 229, 0) 0%,
    rgba(79, 70, 229, 0.3) 50%,
    rgba(79, 70, 229, 0) 100%
  );
}

.stats-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.stats-number {
  font-size: 44rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4f46e5 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 8rpx;
  display: block;
  line-height: 1.2;
}

.stats-label {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
  line-height: 1.3;
}

/* 服务和设置菜单 */
.service-section,
.settings-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-header {
  padding: 32rpx 32rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.menu-list {
  padding: 8rpx 0;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  transition: all 0.3s ease;
}

.menu-item:active {
  background: #f9fafb;
  transform: translateX(8rpx);
}

.menu-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.menu-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-icon-wrapper.blue {
  background: #dbeafe;
}

.menu-icon-wrapper.red {
  background: #fee2e2;
}

.menu-icon-wrapper.yellow {
  background: #fef3c7;
}

.menu-icon-wrapper.purple {
  background: #f3e8ff;
}

.menu-icon-wrapper.gray {
  background: #f3f4f6;
}

.menu-icon-wrapper.green {
  background: #dcfce7;
}

.menu-icon-wrapper.indigo {
  background: #e0e7ff;
}

.menu-icon-wrapper.orange {
  background: #fed7aa;
}

.menu-icon-wrapper.teal {
  background: #ccfbf1;
}

.menu-icon {
  font-size: 32rpx;
}

.menu-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
}

.menu-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.menu-desc {
  font-size: 28rpx;
  color: #6b7280;
}

.menu-arrow {
  font-size: 32rpx;
  color: #9ca3af;
}

/* 退出登录按钮 */
.logout-section {
  margin: 32rpx;
  padding-bottom: 32rpx;
}

.logout-btn {
  width: 100%;
  background: #ffffff;
  border: 2rpx solid #ef4444;
  border-radius: 32rpx;
  padding: 32rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.1);
}

.logout-btn:active {
  background: #ef4444;
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.2);
}

.logout-btn:active .logout-text {
  color: #ffffff;
}

.logout-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #ef4444;
  transition: color 0.3s ease;
}
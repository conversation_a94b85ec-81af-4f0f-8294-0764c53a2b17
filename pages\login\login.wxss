/* pages/login/login.wxss */

.login-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 96rpx 64rpx 32rpx;
  background: #ffffff;
  overflow: hidden;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
}

/* 背景渐变 */
.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 50%, rgba(16, 163, 127, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(0, 0, 0, 0.02) 0%, transparent 50%),
              radial-gradient(circle at 40% 80%, rgba(16, 163, 127, 0.02) 0%, transparent 50%);
  z-index: 1;
}

/* Logo区域 */
.logo-section {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 96rpx;
}

.logo-icon {
  width: 160rpx;
  height: 160rpx;
  border-radius: 24rpx;
  background: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 48rpx;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08),
              0 0 0 1px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.logo-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.logo-icon:hover::before {
  opacity: 1;
}

.logo-icon .icon {
  font-size: 80rpx;
  color: #ffffff;
  z-index: 2;
}

.logo-text {
  text-align: center;
}

.app-name {
  display: block;
  font-size: 60rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 16rpx;
  letter-spacing: -0.5px;
}

.app-desc {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* 欢迎区域 */
.welcome-section {
  position: relative;
  z-index: 10;
  text-align: center;
  margin-bottom: 96rpx;
}

.welcome-title {
  display: block;
  font-size: 48rpx;
  font-weight: 500;
  color: #000000;
  margin-bottom: 24rpx;
  letter-spacing: -0.3px;
}

.welcome-desc {
  display: block;
  font-size: 32rpx;
  color: #6b7280;
  font-weight: 400;
  line-height: 1.5;
  letter-spacing: 0.3px;
}

/* 登录方式区域 */
.login-methods {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 640rpx;
  margin-bottom: 96rpx;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 96rpx;
  background: #ffffff;
  color: #000000;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.03), transparent);
  transition: left 0.5s ease;
}

.login-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.login-btn:hover::before {
  left: 100%;
}

.login-btn:active {
  transform: translateY(1rpx);
}

.btn-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 500;
}

/* 微信登录按钮 */
.wx-login-btn {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: #ffffff;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(31, 41, 55, 0.3);
  margin-bottom: 24rpx;
}

.wx-login-btn:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 32rpx rgba(31, 41, 55, 0.4);
}

/* 手机号授权登录按钮 */
.phone-auth-btn {
  background: transparent;
  color: #1f2937;
  border: 4rpx solid #1f2937;
  box-shadow: none;
}

.phone-auth-btn:hover {
  background: #1f2937;
  color: #ffffff;
  transform: translateY(-4rpx);
}



/* 输入框组 */
.input-group {
  margin-bottom: 32rpx;
}

.input {
  width: 100%;
  height: 96rpx;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 12rpx;
  padding: 0 32rpx;
  font-size: 32rpx;
  color: #000000;
  transition: all 0.2s ease;
}

.input:focus {
  background: #ffffff;
  border-color: #10a37f;
  box-shadow: 0 0 0 3px rgba(16, 163, 127, 0.1);
  outline: none;
}

.input::placeholder {
  color: #9ca3af;
}

/* 验证码输入 */
.code-input-wrapper {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.code-input {
  flex: 1;
}

.send-code-btn {
  height: 96rpx;
  padding: 0 32rpx;
  background: #ffffff;
  color: #000000;
  border: 1px solid #d1d5db;
  border-radius: 12rpx;
  font-size: 28rpx;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-code-btn:not([disabled]):hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.send-code-btn[disabled] {
  opacity: 0.5;
  color: #9ca3af;
  background: #f9fafb;
}



/* 协议区域 - 底部固定 */
.agreement-section {
  position: relative;
  z-index: 10;
  width: 100%;
  margin-top: auto;
  padding: 32rpx 0 0;
}

.agreement-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  width: 100%;
}

.agreement-checkbox {
  margin: 0;
  padding: 0;
  flex-shrink: 0;
  transform: scale(0.8);
}

.agreement-text {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.6;
  text-align: left;
  white-space: nowrap;
  flex: 1;
}

.link-text {
  color: #10a37f;
  text-decoration: none;
  cursor: pointer;
  transition: color 0.2s ease;
}

.link-text:hover {
  color: #059669;
}

/* 装饰元素 */
.decoration-dots {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  overflow: hidden;
}

.dot {
  position: absolute;
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.dot-1 {
  width: 160rpx;
  height: 160rpx;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
  background: radial-gradient(circle, rgba(16, 163, 127, 0.05) 0%, transparent 70%);
}

.dot-2 {
  width: 120rpx;
  height: 120rpx;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
  background: radial-gradient(circle, rgba(0, 0, 0, 0.03) 0%, transparent 70%);
}

.dot-3 {
  width: 80rpx;
  height: 80rpx;
  top: 80%;
  left: 20%;
  animation-delay: 4s;
  background: radial-gradient(circle, rgba(16, 163, 127, 0.04) 0%, transparent 70%);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-40rpx) rotate(180deg);
    opacity: 0.6;
  }
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4rpx);
}

.loading-content {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 64rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid #e5e7eb;
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #10a37f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 32rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #000000;
  text-align: center;
}